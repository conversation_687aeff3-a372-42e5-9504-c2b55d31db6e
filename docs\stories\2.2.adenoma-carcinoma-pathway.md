# Story 2.2: 腺瘤-癌变通路实现

## Status

Draft

## Story

**As a** 模拟引擎，
**I want** 使用可配置参数建模腺瘤-癌变进展通路，
**so that** 准确模拟结直肠癌发展的主要路径（85%的病例）。

## Acceptance Criteria

1. 实现疾病状态枚举（正常→低风险腺瘤→高风险腺瘤→临床前癌症→临床癌症（分为四期））
2. 基于年龄、性别和风险因素的乙状函数腺瘤产生
3. 腺瘤进展的正态分布进展建模
4. 每个疾病状态的停留时间建模
5. 实现性别特异性进展倍数
6. 解剖位置分配（近端结肠、远端结肠、直肠）

## Tasks / Subtasks

- [ ] 任务1：扩展疾病状态枚举系统 (AC: 1)

  - [ ] 扩展src/modules/disease/enums.py，添加详细癌症分期
  - [ ] 实现CancerStage枚举（STAGE_I到STAGE_IV）
  - [ ] 添加AnatomicalLocation枚举（近端、远端、直肠）
  - [ ] 创建疾病状态转换规则和验证
  - [ ] 实现状态转换路径图和文档
  - [ ] 添加状态转换历史跟踪功能
- [ ] 任务2：实现腺瘤产生建模引擎 (AC: 2)

  - [ ] 创建src/modules/disease/adenoma_initiation.py文件
  - [ ] 实现AdenomaInitiationModel类
  - [ ] 添加基于年龄的乙状函数腺瘤产生概率
  - [ ] 实现性别特异性腺瘤产生率
  - [ ] 集成风险因素对腺瘤产生的影响
  - [ ] 添加腺瘤产生时间的随机抽样功能
- [ ] 任务3：实现腺瘤进展建模系统 (AC: 3)

  - [ ] 创建src/modules/disease/adenoma_progression.py文件
  - [ ] 实现AdenomaProgressionModel类
  - [ ] 添加正态分布进展时间建模
  - [ ] 实现低风险到高风险腺瘤转换
  - [ ] 创建高风险腺瘤到临床前癌症转换
  - [ ] 添加进展概率的参数化配置
- [ ] 任务4：实现疾病状态停留时间建模 (AC: 4)

  - [ ] 创建src/modules/disease/dwell_time_model.py文件
  - [ ] 实现DwellTimeModel类，管理状态停留时间
  - [ ] 添加每个疾病状态的停留时间分布
  - [ ] 实现停留时间的随机抽样功能
  - [ ] 创建停留时间参数配置系统
  - [ ] 添加停留时间统计和验证功能
- [ ] 任务5：实现性别特异性进展倍数 (AC: 5)

  - [ ] 扩展进展模型，添加性别特异性参数
  - [ ] 实现男性和女性的不同进展率
  - [ ] 添加性别特异性风险因素权重
  - [ ] 创建性别调整的进展概率计算
  - [ ] 实现性别特异性停留时间分布
  - [ ] 创建性别调整的癌症发病概率计算
  - [ ] 添加性别差异的统计跟踪功能
- [ ] 任务6：实现解剖位置分配系统 (AC: 6)

  - [ ] 创建src/modules/disease/anatomical_location.py文件
  - [ ] 实现AnatomicalLocationAssigner类
  - [ ] 添加基于概率的位置分配（近端40%、远端35%、直肠25%）
  - [ ] 实现位置特异性疾病特征
  - [ ] 创建位置相关的进展参数调整
  - [ ] 添加解剖位置统计和报告功能

## Dev Notes

### 扩展疾病状态枚举

```python
class DiseaseState(Enum):
    NORMAL = "normal"
    LOW_RISK_ADENOMA = "low_risk_adenoma"
    HIGH_RISK_ADENOMA = "high_risk_adenoma"
    PRECLINICAL_CANCER = "preclinical_cancer"
    CLINICAL_CANCER_STAGE_I = "clinical_cancer_stage_i"
    CLINICAL_CANCER_STAGE_II = "clinical_cancer_stage_ii"
    CLINICAL_CANCER_STAGE_III = "clinical_cancer_stage_iii"
    CLINICAL_CANCER_STAGE_IV = "clinical_cancer_stage_iv"
    DEATH_CANCER = "death_cancer"
    DEATH_OTHER = "death_other"

class AnatomicalLocation(Enum):
    PROXIMAL_COLON = "proximal_colon"      # 近端结肠 (40%)
    DISTAL_COLON = "distal_colon"          # 远端结肠 (35%)
    RECTUM = "rectum"                      # 直肠 (25%)
```

### 腺瘤产生乙状函数模型

```python
def calculate_adenoma_initiation_probability(age: float, gender: str, risk_score: float) -> float:
    """计算腺瘤产生概率（乙状函数）"""
    # 基础年龄相关概率（乙状函数）
    base_prob = 1 / (1 + np.exp(-(age - 50) / 10))
  
    # 性别调整
    gender_multiplier = 1.2 if gender == "male" else 1.0
  
    # 风险因素调整
    risk_adjusted_prob = base_prob * gender_multiplier * risk_score
  
    # 年度概率转换
    annual_prob = 1 - (1 - risk_adjusted_prob) ** (1/12)
  
    return min(annual_prob, 0.1)  # 最大月度概率限制
```

### 腺瘤进展时间分布

```python
# 进展时间参数（正态分布）
PROGRESSION_TIMES = {
    "low_to_high_adenoma": {
        "mean": 5.0,      # 平均5年
        "std": 2.0,       # 标准差2年
        "min": 1.0,       # 最小1年
        "max": 15.0       # 最大15年
    },
    "high_adenoma_to_preclinical": {
        "mean": 8.0,      # 平均8年
        "std": 3.0,       # 标准差3年
        "min": 2.0,       # 最小2年
        "max": 20.0       # 最大20年
    },
    "preclinical_to_clinical": {
        "mean": 2.0,      # 平均2年
        "std": 1.0,       # 标准差1年
        "min": 0.5,       # 最小6个月
        "max": 5.0        # 最大5年
    }
}
```

### 性别特异性进展倍数

```python
GENDER_PROGRESSION_MULTIPLIERS = {
    "male": {
        "adenoma_initiation": 1.2,
        "low_to_high_progression": 1.1,
        "high_to_preclinical": 1.15,
        "preclinical_to_clinical": 1.0
    },
    "female": {
        "adenoma_initiation": 1.0,
        "low_to_high_progression": 1.0,
        "high_to_preclinical": 1.0,
        "preclinical_to_clinical": 1.0
    }
}
```

### 解剖位置分配概率

```python
ANATOMICAL_LOCATION_PROBABILITIES = {
    AnatomicalLocation.PROXIMAL_COLON: 0.40,
    AnatomicalLocation.DISTAL_COLON: 0.35,
    AnatomicalLocation.RECTUM: 0.25
}

# 位置特异性特征
LOCATION_SPECIFIC_FEATURES = {
    AnatomicalLocation.PROXIMAL_COLON: {
        "screening_sensitivity_modifier": 0.8,  # 筛查敏感性较低
        "progression_rate_modifier": 1.1,      # 进展稍快
        "cancer_stage_distribution": [0.25, 0.35, 0.25, 0.15]  # I-IV期分布
    },
    AnatomicalLocation.DISTAL_COLON: {
        "screening_sensitivity_modifier": 1.0,  # 标准筛查敏感性
        "progression_rate_modifier": 1.0,      # 标准进展率
        "cancer_stage_distribution": [0.30, 0.40, 0.20, 0.10]
    },
    AnatomicalLocation.RECTUM: {
        "screening_sensitivity_modifier": 1.2,  # 筛查敏感性较高
        "progression_rate_modifier": 0.9,      # 进展稍慢
        "cancer_stage_distribution": [0.35, 0.35, 0.20, 0.10]
    }
}
```

### 疾病状态转换规则

- **正常 → 低风险腺瘤**: 基于年龄、性别、风险因素的乙状函数
- **低风险腺瘤 → 高风险腺瘤**: 正态分布进展时间，平均5年
- **高风险腺瘤 → 临床前癌症**: 正态分布进展时间，平均8年
- **临床前癌症 → 临床癌症**: 正态分布进展时间，平均2年
- **临床癌症**: 分期进展（I→II→III→IV），每期平均1年

### Testing

#### 测试文件位置

- `tests/unit/test_adenoma_initiation.py`
- `tests/unit/test_adenoma_progression.py`
- `tests/unit/test_dwell_time_model.py`
- `tests/integration/test_adenoma_pathway.py`

#### 测试标准

- 腺瘤产生概率计算准确性测试
- 进展时间分布统计检验
- 性别特异性倍数应用测试
- 解剖位置分配概率验证
- 疾病状态转换规则测试

#### 测试框架和模式

- 使用scipy.stats进行分布检验
- Monte Carlo模拟验证概率模型
- 参数化测试验证不同年龄性别组合
- 集成测试验证完整进展路径

#### 特定测试要求

- 概率计算精度: 误差 < 1%
- 分布拟合优度: p值 > 0.05
- 性别差异显著性: 统计检验验证
- 解剖位置分配准确性: 偏差 < 2%

## Change Log

| Date       | Version | Description  | Author       |
| ---------- | ------- | ------------ | ------------ |
| 2025-07-31 | 1.0     | 初始故事创建 | Scrum Master |

## Dev Agent Record

*此部分将由开发代理在实施过程中填写*

### Agent Model Used

*待填写*

### Debug Log References

*待填写*

### Completion Notes List

*待填写*

### File List

*待填写*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填写*
